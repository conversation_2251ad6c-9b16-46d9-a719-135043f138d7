import 'package:flutter/material.dart';
import 'filters.dart';

/// Simple filter status bar that displays active filters and provides clear functionality
class FilterStatusBar extends StatelessWidget {
  final FilterController controller;
  final int? totalCount;
  final int? filteredCount;
  final EdgeInsets? padding;
  final bool showRecordCount;

  const FilterStatusBar({
    Key? key,
    required this.controller,
    this.totalCount,
    this.filteredCount,
    this.padding,
    this.showRecordCount = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        // Debug sort state
        debugPrint('🔍 FilterStatusBar - Sort State Check:');
        debugPrint('🔍 controller.sortBy: ${controller.sortBy}');
        debugPrint('🔍 controller.isAscending: ${controller.isAscending}');
        debugPrint('🔍 controller.pendingSortBy: ${controller.pendingSortBy}');

        // Debug sort arrow visibility
        if (controller.sortBy != null) {
          debugPrint('🔍 FilterStatusBar - Sort Arrow SHOULD APPEAR: sortBy=${controller.sortBy}, isAscending=${controller.isAscending}');
        } else {
          debugPrint('🔍 FilterStatusBar - Sort Arrow NOT APPEARING: sortBy=${controller.sortBy} (is null)');
        }

        // Only show when filters are active
        final hasFilters = controller.searchQuery.isNotEmpty ||
                          controller.startDate != null ||
                          controller.sortBy != null ||
                          controller.hasActiveFilters;

        if (!hasFilters) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: padding ?? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Status text
              Expanded(
                child: Text(
                  showRecordCount && totalCount != null && filteredCount != null
                      ? 'Showing $filteredCount of $totalCount records'
                      : 'Filters active',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey,
                  ),
                ),
              ),

              // Sort direction arrows (if sort is active)
              if (controller.sortBy != null) ...[
                const SizedBox(width: 8),
                GestureDetector(
                  onTap: () {
                    controller.setSortDirection(!controller.isAscending);
                    controller.applyFilters();
                  },
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: controller.isAscending ? Colors.green : Colors.purple,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Icon(
                      controller.isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                      size: 16,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],

              // Clear button
              const SizedBox(width: 8),
              TextButton(
                onPressed: () {
                  debugPrint('🔍 FilterStatusBar - Clear All button pressed');
                  controller.clearAllApplied(); // Clear applied filters immediately
                },
                child: const Text(
                  'Clear All',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
