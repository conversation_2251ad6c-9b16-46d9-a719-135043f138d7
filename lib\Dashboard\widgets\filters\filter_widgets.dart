/// Universal Filter System - UI Components
///
/// This file contains all filter UI components with consistent layouts
/// and styling, including the missing AppFilterWidget.

import 'package:flutter/material.dart';
import 'dart:async';
import 'filters.dart';
import 'filter_data_service.dart';
import '../../../constants/app_layout.dart';

/// Configuration for widget sizing and behavior
class WidgetConfig {
  final bool compact;
  final EdgeInsets? padding;
  final double? height;

  const WidgetConfig({
    this.compact = false,
    this.padding,
    this.height,
  });

  static const WidgetConfig standard = WidgetConfig();
  static const WidgetConfig compactConfig = WidgetConfig(compact: true);
}

/// Filter status bar that displays active filters and provides clear functionality
class FilterStatusBar extends StatelessWidget {
  final FilterController controller;
  final int? totalCount;
  final int? filteredCount;
  final EdgeInsets? padding;
  final bool showRecordCount;

  const FilterStatusBar({
    Key? key,
    required this.controller,
    this.totalCount,
    this.filteredCount,
    this.padding,
    this.showRecordCount = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        final hasFilters = controller.isAnyFilterActive;

        if (!hasFilters) {
          return const SizedBox.shrink();
        }

        return Container(
          margin: padding ?? const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: const Color(0xFFF3E5F5), // Light purple background (no grey)
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                showRecordCount && totalCount != null && filteredCount != null
                    ? 'Showing $filteredCount of $totalCount records'
                    : 'Filters active',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF4A148C), // Dark purple text (no grey)
                ),
              ),
              TextButton(
                onPressed: () {
                  debugPrint('🔍 FilterStatusBar - Clear All button pressed');
                  controller.clearAllApplied(); // Clear applied filters immediately
                },
                child: const Text(
                  'Clear All',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// Universal search widget with debounced input and consistent theming
class UniversalSearchWidget extends StatefulWidget {
  final FilterController controller;
  final String hintText;
  final FilterTheme theme;
  final WidgetConfig config;

  const UniversalSearchWidget({
    Key? key,
    required this.controller,
    required this.hintText,
    required this.theme,
    this.config = WidgetConfig.standard,
  }) : super(key: key);

  @override
  State<UniversalSearchWidget> createState() => _UniversalSearchWidgetState();
}

class _UniversalSearchWidgetState extends State<UniversalSearchWidget> {
  late TextEditingController _textController;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.controller.pendingSearchQuery);
    _textController.addListener(() {
      setState(() {});
    });
  }

  @override
  void didUpdateWidget(UniversalSearchWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (_textController.text != widget.controller.pendingSearchQuery) {
      _textController.text = widget.controller.pendingSearchQuery;
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _textController.dispose();
    super.dispose();
  }

  void _onTextChanged(String text) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (mounted) {
        widget.controller.setSearchQuery(text);
      }
    });
  }

  void _clearSearch() {
    _textController.clear();
    _debounceTimer?.cancel();
    widget.controller.setSearchQuery('');
  }

  @override
  Widget build(BuildContext context) {
    final hasText = _textController.text.isNotEmpty;

    return Container(
      padding: widget.config.padding ?? EdgeInsets.all(widget.config.compact ? 4.0 : 8.0),
      child: Container(
        height: widget.config.height ?? (widget.config.compact ? 44 : 50),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: Colors.white,
          border: Border.all(
            color: hasText
                ? widget.theme.color.withValues(alpha: 0.5)
                : Colors.grey.withValues(alpha: 0.25),
            width: hasText ? 2 : 1.5,
          ),
          boxShadow: hasText
              ? [
                  BoxShadow(
                    color: widget.theme.color.withValues(alpha: 0.15),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.12),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Row(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 18, right: 12),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: hasText
                      ? widget.theme.color.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.08),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.search,
                  size: widget.config.compact ? 18 : 20,
                  color: hasText ? widget.theme.color : Colors.grey[600],
                ),
              ),
            ),
            Expanded(
              child: TextField(
                controller: _textController,
                onChanged: _onTextChanged,
                style: TextStyle(
                  fontSize: widget.config.compact ? 14 : 15,
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
                decoration: InputDecoration(
                  hintText: widget.hintText,
                  hintStyle: TextStyle(
                    color: Colors.grey[500],
                    fontSize: widget.config.compact ? 14 : 15,
                    fontWeight: FontWeight.normal,
                  ),
                  border: InputBorder.none,
                  enabledBorder: InputBorder.none,
                  focusedBorder: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    vertical: widget.config.compact ? 14 : 16,
                  ),
                ),
              ),
            ),
            if (hasText)
              Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _clearSearch,
                    borderRadius: BorderRadius.circular(24),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: widget.theme.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: widget.theme.color.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.close,
                        size: 16,
                        color: widget.theme.color,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// The missing AppFilterWidget for global filters
class AppFilterWidget extends StatefulWidget {
  final FilterController filterController;
  final String moduleName;
  final bool compact;

  const AppFilterWidget({
    Key? key,
    required this.filterController,
    required this.moduleName,
    this.compact = false,
  }) : super(key: key);

  /// Public filter predicate keys - single source of truth for filter keys
  /// These stable keys never change, even if UI labels change
  // Filter keys for 5 filters only (status removed)
  static const String animalTypeKey = 'animaltype';
  static const String breedKey = 'breed';
  static const String genderKey = 'gender';
  static const String cattleKey = 'cattle';
  static const String ageGroupKey = 'agegroup';

  @override
  State<AppFilterWidget> createState() => _AppFilterWidgetState();
}

/// Public interface for AppFilterWidget to allow external control
abstract class AppFilterWidgetController {
  void resetFilters();
}

class _AppFilterWidgetState extends State<AppFilterWidget> implements AppFilterWidgetController {
  final FilterDataService _filterDataService = FilterDataService.instance;

  List<FilterOption> _animalTypeOptions = [];
  List<FilterOption> _breedOptions = [];
  List<FilterOption> _cattleOptions = [];
  bool _isLoading = true;
  bool _isDependentLoading = false;
  String? _selectedAnimalType;
  String? _selectedBreed;

  @override
  void initState() {
    super.initState();
    _loadFilterData();
  }

  /// Reset all filters to initial state and rebuild UI
  @override
  void resetFilters() {
    debugPrint('🔍 AppFilterWidget.resetFilters - Resetting all filters to initial state');

    // Reset internal state
    _selectedAnimalType = null;
    _selectedBreed = null;

    // Clear filter controller pending state
    widget.filterController.clearAll();

    // Reload filter data to reset dependent dropdowns
    _loadDependentFilters();

    // Trigger UI rebuild
    setState(() {});
  }

  /// Get icon for filter type
  IconData _getIconForFilter(String filterKey) {
    // Icons for 5 filter types only (status removed)
    switch (filterKey) {
      case 'animaltype':  // Fixed: use actual filter key
        return Icons.pets;
      case 'breed':
        return Icons.category;
      case 'gender':
        return Icons.person;
      case 'cattle':
        return Icons.agriculture;
      case 'agegroup':  // Fixed: use actual filter key
        return Icons.cake;
      default:
        return Icons.filter_alt;
    }
  }

  /// Get color for filter type
  Color _getColorForFilter(String filterKey) {
    // Multi-color system for 5 filters - NO COLOR REPETITION within this widget
    switch (filterKey) {
      case 'animaltype':  // Fixed: use actual filter key
        return const Color(0xFF2E7D32); // Dark Green
      case 'breed':
        return const Color(0xFF7B1FA2); // Deep Purple
      case 'gender':
        return const Color(0xFF1976D2); // Blue
      case 'cattle':
        return const Color(0xFF00695C); // Teal
      case 'agegroup':  // Fixed: use actual filter key
        return const Color(0xFFD32F2F); // Red
      default:
        return const Color(0xFF4A148C); // Deep Purple variant
    }
  }

  Future<void> _loadFilterData() async {
    try {
      // Load initial data
      final animalTypes = await _filterDataService.getAnimalTypeOptions();

      setState(() {
        _animalTypeOptions = animalTypes;
        _isLoading = false;
      });

      // Load breeds and cattle for initial "All" selection
      await _loadDependentFilters();
    } catch (e) {
      debugPrint('Error loading filter data: $e');
      setState(() {
        _animalTypeOptions = GlobalFilterOptions.fallbackAnimalTypes;
        _breedOptions = GlobalFilterOptions.fallbackBreeds;
        _cattleOptions = GlobalFilterOptions.fallbackCattle;
        _isLoading = false;
      });
    }
  }

  Future<void> _loadDependentFilters() async {
    // Show loading state for dependent dropdowns
    if (!mounted) return;

    setState(() {
      _isDependentLoading = true;
    });

    try {
      final breeds = await _filterDataService.getBreedOptions(animalTypeId: _selectedAnimalType);
      final cattle = await _filterDataService.getCattleOptions(
        animalTypeId: _selectedAnimalType,
        breedId: _selectedBreed,
      );

      if (mounted) {
        setState(() {
          _breedOptions = breeds;
          _cattleOptions = cattle;
          _isDependentLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading dependent filters: $e');
      if (mounted) {
        setState(() {
          _isDependentLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    debugPrint('🔍 AppFilterWidget.build() - Starting build');
    debugPrint('🔍 AppFilterWidget - Module: ${widget.moduleName}, Compact: ${widget.compact}');
    debugPrint('🔍 AppFilterWidget - Loading state: $_isLoading');

    if (_isLoading) {
      debugPrint('🔍 AppFilterWidget - Returning loading indicator');
      return Padding(
        padding: EdgeInsets.all(widget.compact ? 8.0 : 16.0),
        child: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    debugPrint('🔍 AppFilterWidget - Building filter content');
    debugPrint('🔍 AppFilterWidget - Animal types count: ${_animalTypeOptions.length}');
    debugPrint('🔍 AppFilterWidget - Breed options count: ${_breedOptions.length}');
    debugPrint('🔍 AppFilterWidget - Cattle options count: ${_cattleOptions.length}');

    return Container(
      constraints: const BoxConstraints(maxHeight: 500), // Constrain height
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: widget.compact ? 4.0 : 8.0, // Reduced horizontal padding
          vertical: widget.compact ? 4.0 : 8.0,   // Reduced vertical padding
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
          // Animal Type
          _buildFilterDropdown('Animal Type', _animalTypeOptions, filterKey: AppFilterWidget.animalTypeKey, onChanged: _onAnimalTypeChanged),
          const SizedBox(height: 8),

          // Breed (dependent on Animal Type)
          _buildDependentDropdown(
            'Breed',
            _breedOptions,
            filterKey: AppFilterWidget.breedKey,
            onChanged: _onBreedChanged,
          ),
          const SizedBox(height: 8),

          // Gender
          _buildFilterDropdown('Gender', _filterDataService.getGenderOptions(), filterKey: AppFilterWidget.genderKey),
          const SizedBox(height: 8),

          // Cattle (individual cattle selection)
          _buildDependentDropdown(
            'Cattle',
            _cattleOptions,
            filterKey: AppFilterWidget.cattleKey,
          ),

          // Module-specific filters (no divider or header) - 5 filters total
          if (widget.moduleName == 'cattle') ...[
            const SizedBox(height: 8),
            _buildFilterDropdown('Age Group', _filterDataService.getCattleAgeGroupOptions(), filterKey: AppFilterWidget.ageGroupKey),
            // Status field removed - keeping only 5 filters total
          ],
        ],
        ),
      ),
    );
  }

  void _onAnimalTypeChanged(String? value) {
    if (value != null) {
      debugPrint('🔍 _onAnimalTypeChanged - New value: $value');
      _selectedAnimalType = value == 'All' ? null : value;
      _selectedBreed = null; // Reset breed when animal type changes

      // Update filter controller using stable keys
      widget.filterController.updateGlobalFilter(AppFilterWidget.animalTypeKey, value);
      widget.filterController.updateGlobalFilter(AppFilterWidget.breedKey, 'All'); // Reset breed filter
      widget.filterController.updateGlobalFilter(AppFilterWidget.cattleKey, 'All'); // Reset cattle filter

      // Reload dependent filters and rebuild UI
      _loadDependentFilters();
    }
  }

  void _onBreedChanged(String? value) {
    if (value != null) {
      _selectedBreed = value == 'All' ? null : value;

      // Update filter controller using stable keys
      widget.filterController.updateGlobalFilter(AppFilterWidget.breedKey, value);
      widget.filterController.updateGlobalFilter(AppFilterWidget.cattleKey, 'All'); // Reset cattle filter

      // Reload cattle options
      _loadDependentFilters();
    }
  }

  /// Build a dependent dropdown with loading indicator
  Widget _buildDependentDropdown(String label, List<FilterOption> options, {required String filterKey, Function(String?)? onChanged}) {
    debugPrint('🔍 _buildDependentDropdown - Label: $label, Options: ${options.length}, Loading: $_isDependentLoading');

    return Stack(
      children: [
        _buildFilterDropdown(label, options, filterKey: filterKey, onChanged: onChanged),
        if (_isDependentLoading)
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.green),
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFilterDropdown(String label, List<FilterOption> options, {required String filterKey, Function(String?)? onChanged}) {
    final currentValue = widget.filterController.pendingGlobalFilters[filterKey] ?? options.first.value;
    debugPrint('🔍 _buildFilterDropdown - Label: $label, Current: $currentValue, Options: ${options.length}');

    return UniversalFormField.dropdownField<String>(
      label: label,
      value: options.any((option) => option.value == currentValue) ? currentValue : options.first.value,
      items: options.map((option) {
        return DropdownMenuItem<String>(
          value: option.value,
          child: Text(
            option.label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black, // Solid dark color for text (not light color)
            ),
            overflow: TextOverflow.ellipsis,
          ),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          debugPrint('🔍 Dropdown selection - Filter: $filterKey, Value: $value');
          widget.filterController.updateGlobalFilter(filterKey, value);
          onChanged?.call(value);
          // Trigger UI rebuild to show the new selection
          setState(() {});
        }
      },
      prefixIcon: _getIconForFilter(filterKey),
      prefixIconColor: _getColorForFilter(filterKey),
      labelColor: _getColorForFilter(filterKey), // Make floating label use same color as icon
    );
  }


}

/// Universal date filter widget with predefined ranges - matches filter dialog UI
class UniversalDateFilterWidget extends StatelessWidget {
  final FilterController controller;
  final FilterTheme theme;
  final WidgetConfig config;

  const UniversalDateFilterWidget({
    Key? key,
    required this.controller,
    required this.theme,
    this.config = WidgetConfig.standard,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    debugPrint('🔍 UniversalDateFilterWidget.build() - Starting build');
    debugPrint('🔍 UniversalDateFilterWidget - Pending dates: ${controller.pendingStartDate} to ${controller.pendingEndDate}');

    return Container(
      padding: config.padding ?? const EdgeInsets.all(16),
      constraints: const BoxConstraints(maxHeight: 500),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Predefined ranges with radio buttons
          Flexible(
            child: _buildPredefinedRanges(),
          ),
          const SizedBox(height: 16),

          // Custom range colored button
          _buildCustomRangeSection(context),
        ],
      ),
    );
  }

  Widget _buildPredefinedRanges() {
    final predefinedRanges = FilterHandlers.getPredefinedDateRanges();

    // Define unique colors for each date range option
    final rangeColors = [
      const Color(0xFF2E7D32), // Dark Green
      const Color(0xFF7B1FA2), // Deep Purple
      const Color(0xFF1976D2), // Blue
      const Color(0xFFD32F2F), // Red
    ];

    return Column(
      children: predefinedRanges.asMap().entries.map((entry) {
        final index = entry.key;
        final range = entry.value;
        final rangeColor = rangeColors[index % rangeColors.length];

        return RadioListTile<String>(
          value: '${range.startDate.millisecondsSinceEpoch}_${range.endDate.millisecondsSinceEpoch}',
          groupValue: controller.pendingStartDate != null && controller.pendingEndDate != null
              ? '${controller.pendingStartDate!.millisecondsSinceEpoch}_${controller.pendingEndDate!.millisecondsSinceEpoch}'
              : null,
          onChanged: (value) => controller.setDateRange(range.startDate, range.endDate),
          activeColor: rangeColor,
          title: Text(
            range.label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: rangeColor, // Colored name
            ),
          ),

        );
      }).toList(),
    );
  }

  Widget _buildCustomRangeSection(BuildContext context) {
    const customColor = Color(0xFF00695C); // Teal color for custom section

    return ElevatedButton(
      onPressed: () => _showCustomDatePicker(context),
      style: ElevatedButton.styleFrom(
        backgroundColor: customColor,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        'Custom Date Range',
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  bool _isAnyPredefinedRangeSelected() {
    if (controller.pendingStartDate == null || controller.pendingEndDate == null) {
      return false;
    }

    final predefinedRanges = FilterHandlers.getPredefinedDateRanges();
    return predefinedRanges.any((range) => _isRangeSelected(range));
  }

  bool _isRangeSelected(DateRangeOption range) {
    if (controller.pendingStartDate == null || controller.pendingEndDate == null) {
      return false;
    }

    final startMatches = controller.pendingStartDate!.year == range.startDate.year &&
                        controller.pendingStartDate!.month == range.startDate.month &&
                        controller.pendingStartDate!.day == range.startDate.day;

    final endMatches = controller.pendingEndDate!.year == range.endDate.year &&
                      controller.pendingEndDate!.month == range.endDate.month &&
                      controller.pendingEndDate!.day == range.endDate.day;

    return startMatches && endMatches;
  }

  Future<void> _showCustomDatePicker(BuildContext context) async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: controller.pendingStartDate != null && controller.pendingEndDate != null
          ? DateTimeRange(start: controller.pendingStartDate!, end: controller.pendingEndDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(primary: theme.color),
          ),
          child: child!,
        );
      },
    );

    if (dateRange != null) {
      controller.setDateRange(dateRange.start, dateRange.end);
    }
  }
}

/// Universal sort widget with field selection and direction toggle - matches filter dialog UI
class UniversalSortWidget extends StatelessWidget {
  final FilterController controller;
  final List<SortField> sortFields;
  final FilterTheme theme;
  final WidgetConfig config;

  const UniversalSortWidget({
    Key? key,
    required this.controller,
    required this.sortFields,
    required this.theme,
    this.config = WidgetConfig.standard,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: config.padding ?? const EdgeInsets.all(16),
      constraints: const BoxConstraints(maxHeight: 500),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sort direction radio buttons
          _buildSortDirectionToggle(),
          const SizedBox(height: 20),

          // Sort field radio buttons
          Flexible(
            child: _buildSortOptionsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSortDirectionToggle() {
    const ascendingColor = Color(0xFF2E7D32); // Dark Green
    const descendingColor = Color(0xFF7B1FA2); // Deep Purple

    return Column(
      children: [
        // Ascending radio button
        RadioListTile<bool>(
          value: true,
          groupValue: controller.pendingIsAscending,
          onChanged: (value) => controller.setSortDirection(true),
          activeColor: ascendingColor,
          title: Text(
            'Ascending',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ascendingColor, // Colored name
            ),
          ),
        ),
        // Descending radio button
        RadioListTile<bool>(
          value: false,
          groupValue: controller.pendingIsAscending,
          onChanged: (value) => controller.setSortDirection(false),
          activeColor: descendingColor,
          title: Text(
            'Descending',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: descendingColor, // Colored name
            ),
          ),
        ),
      ],
    );
  }



  Widget _buildSortOptionsList() {
    // Define unique colors for each sort field (no repetition)
    final sortColors = [
      const Color(0xFF1976D2), // Blue
      const Color(0xFFD32F2F), // Red
      const Color(0xFF00695C), // Teal
      const Color(0xFF7B1FA2), // Deep Purple
      const Color(0xFF2E7D32), // Dark Green
    ];

    return Column(
      children: sortFields.asMap().entries.map((entry) {
        final index = entry.key;
        final field = entry.value;
        final fieldColor = sortColors[index % sortColors.length];

        return RadioListTile<String>(
          value: field.key,
          groupValue: controller.pendingSortBy,
          onChanged: (value) => controller.setSortBy(value!),
          activeColor: fieldColor,
          title: Text(
            field.label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: fieldColor, // Colored name
            ),
          ),
        );
      }).toList(),
    );
  }
}
